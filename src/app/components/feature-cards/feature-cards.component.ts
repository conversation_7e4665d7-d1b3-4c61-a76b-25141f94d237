import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

interface FeatureCard {
  id: string;
  title: string;
  description: string;
  icon: string;
  buttonText: string;
  buttonIcon: string;
}

@Component({
  selector: 'app-feature-cards',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './feature-cards.component.html',
  styleUrl: './feature-cards.component.scss'
})
export class FeatureCardsComponent {
  featureCards: FeatureCard[] = [
    {
      id: 'calendar',
      title: 'Family Calendar',
      description: 'View and manage shared events.',
      icon: 'calendar',
      buttonText: 'Open Calendar',
      buttonIcon: 'arrow'
    },
    {
      id: 'media',
      title: 'Media Sharing',
      description: 'Share photos and videos.',
      icon: 'media',
      buttonText: 'View Media',
      buttonIcon: 'arrow'
    },
    {
      id: 'files',
      title: 'File Vault',
      description: 'Store important documents securely.',
      icon: 'files',
      buttonText: 'Open Files',
      buttonIcon: 'arrow'
    },
    {
      id: 'lists',
      title: 'Shared Lists',
      description: 'Collaborate on to-do and shopping lists.',
      icon: 'lists',
      buttonText: 'Manage Lists',
      buttonIcon: 'arrow'
    },
    {
      id: 'journal',
      title: 'Family Journal',
      description: 'Share updates and family news.',
      icon: 'journal',
      buttonText: 'Read Journal',
      buttonIcon: 'arrow'
    },
    {
      id: 'invitations',
      title: 'AI Invitations',
      description: 'Create smart invitations with AI.',
      icon: 'invitations',
      buttonText: 'Create Invitation',
      buttonIcon: 'arrow'
    }
  ];

  onFeatureClick(feature: FeatureCard): void {
    console.log('Feature clicked:', feature.id);
    // Here you would typically navigate to the feature
  }
}
